{"extends": ["stylelint-config-standard", "stylelint-config-recess-order"], "plugins": ["stylelint-order"], "rules": {"comment-empty-line-before": ["always", {"except": ["first-nested"], "ignore": ["after-comment", "stylelint-commands"]}], "custom-property-empty-line-before": ["always", {"except": ["after-custom-property", "first-nested"], "ignore": ["after-comment", "inside-single-line-block"]}], "custom-property-pattern": ["^(bg|text|accent|glass|border|neon|dark|shadow|spring|micro|blur|opacity)-.+", {"message": "Expected custom property name to follow naming convention: --{category}-{name}"}], "declaration-empty-line-before": ["always", {"except": ["after-declaration", "first-nested"], "ignore": ["after-comment", "inside-single-line-block"]}], "rule-empty-line-before": ["always-multi-line", {"except": ["first-nested"], "ignore": ["after-comment"]}], "selector-class-pattern": ["^([a-z][a-z0-9]*)(-[a-z0-9]+)*(__[a-z0-9]+(-[a-z0-9]+)*)?(--[a-z0-9]+(-[a-z0-9]+)*)?$", {"message": "Expected class selector to follow BEM naming convention"}], "selector-id-pattern": ["^[a-z][a-z0-9]*(-[a-z0-9]+)*$", {"message": "Expected id selector to be kebab-case"}], "unit-allowed-list": ["px", "rem", "em", "%", "vh", "vw", "vmin", "vmax", "deg", "ms", "s", "fr", "ch", "ex", "dpi", "dppx"], "color-hex-length": "short", "value-keyword-case": "lower", "function-name-case": "lower", "no-duplicate-selectors": true, "no-empty-source": true, "block-no-empty": true, "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates-with-different-values"]}], "no-descending-specificity": null, "selector-max-id": 1, "selector-max-universal": 1, "selector-max-type": 3, "selector-max-class": 4, "selector-max-combinators": 3, "selector-max-compound-selectors": 4, "max-nesting-depth": 3, "declaration-block-single-line-max-declarations": 1, "length-zero-no-unit": true, "shorthand-property-no-redundant-values": true, "declaration-block-no-redundant-longhand-properties": true, "font-weight-notation": "numeric", "color-function-notation": "modern", "alpha-value-notation": "number", "hue-degree-notation": "angle", "import-notation": "string", "keyframe-selector-notation": "percentage-unless-within-keyword-only-block", "selector-not-notation": "complex", "selector-pseudo-element-colon-notation": "double"}, "ignoreFiles": ["build/**/*", "dist/**/*", "node_modules/**/*"]}